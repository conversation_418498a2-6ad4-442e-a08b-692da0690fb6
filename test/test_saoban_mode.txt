# 扫板模式触发测试
# 测试场景：涨停价格 > ask_price[0] + 900 时触发扫板模式，阈值设为0

# 1. 初始tick，涨停价110000，卖1价108000，差价2000 > 900，触发扫板模式
[93000000]|SH.600002|TICK|V:100000|T:93000000|M:109000|PC:100000|TAV:50000|BV1:200000|BP1:109000|AP1:108000

# 2. 订阅股票（在测试代码中会自动订阅）

# 3. 逐笔买入委托，扫板模式下阈值为0，应该立即下单
[93000100]|SH.600002|BW|V:50000|T:93000100|P:110000

# 4. 后续tick，涨停价格仍然 > ask_price[0] + 900，保持扫板模式
[93003000]|SH.600002|TICK|V:150000|T:93003000|M:109500|PC:100000|TAV:30000|BV1:250000|BP1:109500|AP1:108000
